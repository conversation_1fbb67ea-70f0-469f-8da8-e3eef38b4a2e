#!/usr/bin/env python3
"""
简化的回归批量功能测试脚本

测试核心逻辑，不依赖PyQt5
"""

import os
import sys
import tempfile
import shutil
import re
from pathlib import Path
from dataclasses import dataclass
from typing import List, Dict, Optional


@dataclass
class RegressionFileInfo:
    """回归文件信息"""
    file_path: str              # 完整文件路径
    subsys: str                 # 子系统名称
    corner_name: str            # 工艺角名称
    case_name: str              # 用例名称
    seed: str                   # 种子号
    relative_path: str          # 相对于回归根目录的路径
    file_size: int              # 文件大小（字节）
    modified_time: float        # 文件修改时间


class SimpleRegressionScanner:
    """简化的回归扫描器（不依赖PyQt5）"""
    
    def __init__(self):
        # 目录结构正则表达式
        self.case_corner_pattern = re.compile(r'([^_]+)_([^/\\]+)$')
        self.case_seed_pattern = re.compile(r'([^_]+)_(\d+)$')
        
        # 子系统识别模式
        self.subsys_patterns = [
            re.compile(r'.*_sys$'),  # 以_sys结尾
            re.compile(r'^top$'),    # top目录
            re.compile(r'.*_subsys$'), # 以_subsys结尾
        ]
    
    def scan_regression_directory(self, regression_root: str) -> List[RegressionFileInfo]:
        """扫描回归目录"""
        if not os.path.exists(regression_root):
            raise FileNotFoundError(f"回归目录不存在: {regression_root}")
        
        valid_files = []
        
        # 递归扫描目录
        for root, dirs, files in os.walk(regression_root):
            if 'vio_summary.log' in files:
                file_path = os.path.join(root, 'vio_summary.log')
                file_info = self._parse_file_path(file_path, regression_root)
                if file_info:
                    valid_files.append(file_info)
        
        return valid_files
    
    def _parse_file_path(self, file_path: str, regression_root: str) -> Optional[RegressionFileInfo]:
        """解析文件路径，提取目录结构信息"""
        try:
            # 获取相对路径
            relative_path = os.path.relpath(file_path, regression_root)
            
            # 分割路径组件
            path_parts = relative_path.split(os.sep)
            
            # 至少需要4层：subsys/.../case_corner/case_seed/log/vio_summary.log
            if len(path_parts) < 4:
                return None
            
            # 最后一个应该是vio_summary.log
            if path_parts[-1] != 'vio_summary.log':
                return None
            
            # 倒数第二个应该是log目录
            if path_parts[-2] != 'log':
                return None
            
            # 倒数第三个应该是case_seed格式
            case_seed_dir = path_parts[-3]
            case_seed_match = self.case_seed_pattern.match(case_seed_dir)
            if not case_seed_match:
                return None
            
            case_name = case_seed_match.group(1)
            seed = case_seed_match.group(2)
            
            # 倒数第四个应该是case_corner格式
            case_corner_dir = path_parts[-4]
            case_corner_match = self.case_corner_pattern.match(case_corner_dir)
            if not case_corner_match:
                return None
            
            case_name_from_corner = case_corner_match.group(1)
            corner_name = case_corner_match.group(2)
            
            # 验证case名称一致性
            if case_name != case_name_from_corner:
                return None
            
            # 查找子系统名称
            subsys = self._find_subsys_in_path(path_parts[:-4])
            if not subsys:
                # 如果没有找到明确的子系统，使用第一级目录作为子系统
                subsys = path_parts[0] if path_parts else "unknown"
            
            # 获取文件信息
            file_size = os.path.getsize(file_path)
            modified_time = os.path.getmtime(file_path)
            
            return RegressionFileInfo(
                file_path=file_path,
                subsys=subsys,
                corner_name=corner_name,
                case_name=case_name,
                seed=seed,
                relative_path=relative_path,
                file_size=file_size,
                modified_time=modified_time
            )
            
        except Exception as e:
            return None
    
    def _find_subsys_in_path(self, path_parts: List[str]) -> Optional[str]:
        """在路径组件中查找子系统名称"""
        for part in path_parts:
            for pattern in self.subsys_patterns:
                if pattern.match(part):
                    return part
        return None


def create_test_regression_structure():
    """创建测试用的回归目录结构"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="test_regression_")
    print(f"创建测试目录: {temp_dir}")
    
    # 创建回归目录结构
    test_structure = {
        "cpu_sys": {
            "cpu_test_ss": {
                "cpu_test_seed1": "log/vio_summary.log",
                "cpu_test_seed2": "log/vio_summary.log"
            },
            "cpu_test_ff": {
                "cpu_test_seed1": "log/vio_summary.log",
                "cpu_test_seed3": "log/vio_summary.log"
            }
        },
        "memory_sys": {
            "mem_test_tt": {
                "mem_test_seed1": "log/vio_summary.log",
                "mem_test_seed2": "log/vio_summary.log"
            }
        },
        "top": {
            "integration_test_ss": {
                "integration_test_seed1": "log/vio_summary.log"
            }
        }
    }
    
    # 创建目录和文件
    for subsys, corners in test_structure.items():
        for corner, seeds in corners.items():
            for seed, log_path in seeds.items():
                full_path = os.path.join(temp_dir, subsys, corner, seed, log_path)
                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                
                # 创建示例vio_summary.log文件
                with open(full_path, 'w') as f:
                    f.write(create_sample_vio_log(subsys, corner.split('_')[-1], seed.split('_')[-1]))
    
    return temp_dir


def create_sample_vio_log(subsys, corner, seed):
    """创建示例违例日志内容"""
    return f"""# Timing Violation Summary
# Subsystem: {subsys}
# Corner: {corner}
# Seed: {seed}

NUM  Hier                                    Time        Check
1    {subsys}.cpu.clk_domain                100.5 NS    setup
2    {subsys}.cpu.data_path                 150.2 NS    hold
3    {subsys}.memory.ctrl                   200.8 NS    setup
4    {subsys}.interface.bus                 75.3 NS     hold
5    {subsys}.debug.trace                   300.1 NS    setup
"""


def test_regression_scanner():
    """测试回归扫描器"""
    print("\n=== 测试回归扫描器 ===")
    
    # 创建测试目录
    test_dir = create_test_regression_structure()
    
    try:
        # 创建扫描器
        scanner = SimpleRegressionScanner()
        
        # 扫描目录
        print(f"扫描目录: {test_dir}")
        files = scanner.scan_regression_directory(test_dir)
        
        # 输出结果
        print(f"发现文件数: {len(files)}")
        
        # 按子系统分组
        subsys_groups = {}
        corner_groups = {}
        case_groups = {}
        
        for file_info in files:
            # 子系统分组
            if file_info.subsys not in subsys_groups:
                subsys_groups[file_info.subsys] = []
            subsys_groups[file_info.subsys].append(file_info)
            
            # 工艺角分组
            if file_info.corner_name not in corner_groups:
                corner_groups[file_info.corner_name] = []
            corner_groups[file_info.corner_name].append(file_info)
            
            # 用例分组
            if file_info.case_name not in case_groups:
                case_groups[file_info.case_name] = []
            case_groups[file_info.case_name].append(file_info)
        
        print("\n子系统分组:")
        for subsys, files_list in subsys_groups.items():
            print(f"  {subsys}: {len(files_list)} 个文件")
        
        print("\n工艺角分组:")
        for corner, files_list in corner_groups.items():
            print(f"  {corner}: {len(files_list)} 个文件")
        
        print("\n用例分组:")
        for case, files_list in case_groups.items():
            print(f"  {case}: {len(files_list)} 个文件")
        
        print("\n详细文件信息:")
        for file_info in files:
            print(f"  {file_info.subsys}/{file_info.corner_name}/{file_info.case_name}_{file_info.seed}")
        
        return files
        
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)
        print(f"清理测试目录: {test_dir}")


def main():
    """主测试函数"""
    print("开始回归批量功能测试（简化版）")
    
    try:
        # 测试扫描器
        files = test_regression_scanner()
        
        print("\n=== 测试完成 ===")
        print("所有测试通过！")
        print(f"成功扫描到 {len(files)} 个文件")
        
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
