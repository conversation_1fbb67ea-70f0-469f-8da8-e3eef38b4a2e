"""
回归批量管理器

管理回归目录扫描结果，提供文件选择、批量处理和数据组织功能。
"""

import os
import time
from typing import List, Dict, Optional, Set, Tuple
from dataclasses import dataclass
from PyQt5.QtCore import QObject, pyqtSignal

from .regression_scanner import RegressionFileInfo, RegressionScanResult


@dataclass
class BatchProcessConfig:
    """批量处理配置"""
    max_concurrent_files: int = 3      # 最大并发文件数
    chunk_size: int = 1000             # 每个文件的分块大小
    memory_limit_mb: float = 1000.0    # 内存限制（MB）
    enable_progress_tracking: bool = True  # 启用进度跟踪
    auto_group_by_corner: bool = True  # 自动按corner分组


@dataclass
class BatchProcessResult:
    """批量处理结果"""
    total_files: int                    # 总文件数
    processed_files: int                # 已处理文件数
    total_violations: int               # 总违例数
    violations_by_file: Dict[str, int]  # 每个文件的违例数
    processing_time: float              # 处理时间
    errors: List[str]                   # 错误信息


class RegressionBatchManager(QObject):
    """回归批量管理器"""
    
    # 信号定义
    selection_changed = pyqtSignal(list)    # 选择变更，返回选中的文件列表
    batch_progress = pyqtSignal(int, str)   # 批量处理进度
    batch_completed = pyqtSignal(object)    # 批量处理完成，返回BatchProcessResult
    batch_failed = pyqtSignal(str)          # 批量处理失败
    
    def __init__(self):
        super().__init__()
        
        # 扫描结果
        self.scan_result: Optional[RegressionScanResult] = None
        
        # 选择状态
        self.selected_files: Set[str] = set()  # 选中的文件路径
        self.selection_filters = {
            'subsys': set(),      # 选中的子系统
            'corner': set(),      # 选中的工艺角
            'case': set(),        # 选中的用例
        }
        
        # 批量处理配置
        self.batch_config = BatchProcessConfig()
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'selected_files': 0,
            'estimated_violations': 0,
            'estimated_processing_time': 0.0
        }
    
    def set_scan_result(self, scan_result: RegressionScanResult):
        """设置扫描结果"""
        self.scan_result = scan_result
        self.selected_files.clear()
        self.selection_filters = {
            'subsys': set(),
            'corner': set(),
            'case': set(),
        }
        self._update_stats()
    
    def get_subsys_list(self) -> List[str]:
        """获取子系统列表"""
        if not self.scan_result:
            return []
        return sorted(self.scan_result.subsys_groups.keys())
    
    def get_corner_list(self, subsys_filter: Optional[Set[str]] = None) -> List[str]:
        """获取工艺角列表"""
        if not self.scan_result:
            return []
        
        corners = set()
        for file_info in self.scan_result.valid_files:
            if subsys_filter is None or file_info.subsys in subsys_filter:
                corners.add(file_info.corner_name)
        
        return sorted(corners)
    
    def get_case_list(self, subsys_filter: Optional[Set[str]] = None, 
                     corner_filter: Optional[Set[str]] = None) -> List[str]:
        """获取用例列表"""
        if not self.scan_result:
            return []
        
        cases = set()
        for file_info in self.scan_result.valid_files:
            if (subsys_filter is None or file_info.subsys in subsys_filter) and \
               (corner_filter is None or file_info.corner_name in corner_filter):
                cases.add(file_info.case_name)
        
        return sorted(cases)
    
    def get_files_by_filters(self) -> List[RegressionFileInfo]:
        """根据当前过滤器获取文件列表"""
        if not self.scan_result:
            return []
        
        filtered_files = []
        for file_info in self.scan_result.valid_files:
            # 应用过滤器
            if self.selection_filters['subsys'] and file_info.subsys not in self.selection_filters['subsys']:
                continue
            if self.selection_filters['corner'] and file_info.corner_name not in self.selection_filters['corner']:
                continue
            if self.selection_filters['case'] and file_info.case_name not in self.selection_filters['case']:
                continue
            
            filtered_files.append(file_info)
        
        return filtered_files
    
    def set_subsys_filter(self, subsys_list: List[str]):
        """设置子系统过滤器"""
        self.selection_filters['subsys'] = set(subsys_list)
        self._update_selection()
    
    def set_corner_filter(self, corner_list: List[str]):
        """设置工艺角过滤器"""
        self.selection_filters['corner'] = set(corner_list)
        self._update_selection()
    
    def set_case_filter(self, case_list: List[str]):
        """设置用例过滤器"""
        self.selection_filters['case'] = set(case_list)
        self._update_selection()
    
    def select_files(self, file_paths: List[str]):
        """选择文件"""
        self.selected_files.update(file_paths)
        self._update_stats()
        self._emit_selection_changed()
    
    def deselect_files(self, file_paths: List[str]):
        """取消选择文件"""
        self.selected_files.difference_update(file_paths)
        self._update_stats()
        self._emit_selection_changed()
    
    def select_all_filtered(self):
        """选择所有过滤后的文件"""
        filtered_files = self.get_files_by_filters()
        file_paths = [f.file_path for f in filtered_files]
        self.selected_files.update(file_paths)
        self._update_stats()
        self._emit_selection_changed()
    
    def clear_selection(self):
        """清空选择"""
        self.selected_files.clear()
        self._update_stats()
        self._emit_selection_changed()
    
    def get_selected_files(self) -> List[RegressionFileInfo]:
        """获取选中的文件信息"""
        if not self.scan_result:
            return []
        
        selected_files = []
        for file_info in self.scan_result.valid_files:
            if file_info.file_path in self.selected_files:
                selected_files.append(file_info)
        
        return selected_files
    
    def estimate_processing_time(self) -> float:
        """估算处理时间"""
        selected_files = self.get_selected_files()
        if not selected_files:
            return 0.0
        
        # 基于文件大小估算处理时间
        # 假设每MB需要0.1秒处理时间
        total_size_mb = sum(f.file_size for f in selected_files) / (1024 * 1024)
        estimated_time = total_size_mb * 0.1
        
        # 考虑并发处理
        if self.batch_config.max_concurrent_files > 1:
            estimated_time /= min(self.batch_config.max_concurrent_files, len(selected_files))
        
        return estimated_time
    
    def estimate_violations_count(self) -> int:
        """估算违例数量"""
        selected_files = self.get_selected_files()
        if not selected_files:
            return 0
        
        # 基于文件大小估算违例数量
        # 假设每KB包含1个违例
        total_size_kb = sum(f.file_size for f in selected_files) / 1024
        estimated_violations = int(total_size_kb)
        
        return estimated_violations
    
    def get_statistics(self) -> Dict[str, any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def _update_selection(self):
        """更新选择状态"""
        # 根据过滤器更新选择
        filtered_files = self.get_files_by_filters()
        filtered_paths = {f.file_path for f in filtered_files}
        
        # 移除不在过滤结果中的选择
        self.selected_files.intersection_update(filtered_paths)
        
        self._update_stats()
        self._emit_selection_changed()
    
    def _update_stats(self):
        """更新统计信息"""
        if not self.scan_result:
            self.stats = {
                'total_files': 0,
                'selected_files': 0,
                'estimated_violations': 0,
                'estimated_processing_time': 0.0
            }
            return
        
        self.stats = {
            'total_files': len(self.scan_result.valid_files),
            'selected_files': len(self.selected_files),
            'estimated_violations': self.estimate_violations_count(),
            'estimated_processing_time': self.estimate_processing_time()
        }
    
    def _emit_selection_changed(self):
        """发送选择变更信号"""
        selected_files = self.get_selected_files()
        self.selection_changed.emit(selected_files)
    
    def set_batch_config(self, config: BatchProcessConfig):
        """设置批量处理配置"""
        self.batch_config = config
        self._update_stats()  # 重新计算估算时间
