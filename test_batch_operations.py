#!/usr/bin/env python3
"""
测试批量操作功能
"""
import sys
import time
from typing import Dict, List
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import QTimer

# 添加utils路径
sys.path.append('utils')

from utils.batch_operations_manager import ViolationBatchProcessor, BatchOperationConfig
from utils.batch_progress_dialog import BatchProgressDialog


class MockDataModel:
    """模拟数据模型"""
    
    def __init__(self):
        self.confirmations = {}
        self.patterns = []
    
    def update_confirmation(self, violation_id, status, confirmer, result, reason, is_auto=False):
        """模拟更新确认"""
        # 模拟一些处理时间
        time.sleep(0.01)
        
        # 模拟90%的成功率
        import random
        success = random.random() < 0.9
        
        if success:
            self.confirmations[violation_id] = {
                'status': status,
                'confirmer': confirmer,
                'result': result,
                'reason': reason,
                'is_auto': is_auto
            }
        
        return success
    
    def save_pattern(self, hier, check_info, confirmer, result, reason):
        """模拟保存模式"""
        self.patterns.append({
            'hier': hier,
            'check_info': check_info,
            'confirmer': confirmer,
            'result': result,
            'reason': reason
        })


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("批量操作测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建模拟数据模型
        self.data_model = MockDataModel()
        
        # 创建批量处理器
        self.batch_processor = ViolationBatchProcessor(self.data_model, self)
        
        # 设置UI
        self.setup_ui()
        
        # 生成测试数据
        self.test_violations = self.generate_test_violations(1000)
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 测试按钮
        self.test_small_batch_btn = QPushButton("测试小批量确认 (50个)")
        self.test_small_batch_btn.clicked.connect(self.test_small_batch)
        layout.addWidget(self.test_small_batch_btn)
        
        self.test_large_batch_btn = QPushButton("测试大批量确认 (500个)")
        self.test_large_batch_btn.clicked.connect(self.test_large_batch)
        layout.addWidget(self.test_large_batch_btn)
        
        self.test_status_update_btn = QPushButton("测试状态更新 (200个)")
        self.test_status_update_btn.clicked.connect(self.test_status_update)
        layout.addWidget(self.test_status_update_btn)
        
        self.test_export_btn = QPushButton("测试导出 (1000个)")
        self.test_export_btn.clicked.connect(self.test_export)
        layout.addWidget(self.test_export_btn)
    
    def generate_test_violations(self, count: int) -> List[Dict]:
        """生成测试违例数据"""
        violations = []
        for i in range(count):
            violation = {
                'id': i + 1,
                'hier': f'test/hierarchy/path_{i}',
                'check_info': f'setup_check_{i % 10}',
                'status': 'pending',
                'slack': f'-{i % 100}ps',
                'required': f'{1000 + i}ps',
                'actual': f'{1000 + i + (i % 100)}ps',
                'confirmer': '',
                'result': '',
                'reason': '',
                'timestamp': f'2024-01-01 10:{i % 60:02d}:00'
            }
            violations.append(violation)
        return violations
    
    def test_small_batch(self):
        """测试小批量确认"""
        violations = self.test_violations[:50]
        confirmation_data = {
            'confirmer': 'test_user',
            'result': 'waived',
            'reason': '测试小批量确认'
        }
        
        print(f"开始测试小批量确认：{len(violations)} 个违例")
        
        # 直接处理（不显示进度对话框）
        success_count = 0
        for violation in violations:
            if violation.get('status') == 'pending':
                result = self.data_model.update_confirmation(
                    violation['id'],
                    status='confirmed',
                    confirmer=confirmation_data['confirmer'],
                    result=confirmation_data['result'],
                    reason=confirmation_data['reason'],
                    is_auto=False
                )
                if result:
                    success_count += 1
        
        print(f"小批量确认完成：成功 {success_count} 个")
    
    def test_large_batch(self):
        """测试大批量确认"""
        violations = self.test_violations[:500]
        confirmation_data = {
            'confirmer': 'test_user',
            'result': 'waived',
            'reason': '测试大批量确认'
        }
        
        print(f"开始测试大批量确认：{len(violations)} 个违例")
        
        # 创建进度对话框
        progress_dialog = BatchProgressDialog(self, "测试批量确认违例")
        
        # 连接信号
        self.batch_processor.batch_manager.progress_updated.connect(progress_dialog.update_progress)
        self.batch_processor.batch_manager.operation_completed.connect(progress_dialog.on_operation_completed)
        self.batch_processor.batch_manager.operation_completed.connect(self.on_test_completed)
        
        # 连接取消信号
        progress_dialog.cancel_requested.connect(self.batch_processor.cancel_batch_operation)
        
        # 启动批量操作
        success, message = self.batch_processor.batch_confirm_violations(violations, confirmation_data)
        
        if success:
            progress_dialog.exec_()
        else:
            print(f"无法启动批量确认操作：{message}")
    
    def test_status_update(self):
        """测试状态更新"""
        violations = self.test_violations[100:300]  # 200个违例
        
        print(f"开始测试状态更新：{len(violations)} 个违例")
        
        # 创建进度对话框
        progress_dialog = BatchProgressDialog(self, "测试批量状态更新")
        
        # 连接信号
        self.batch_processor.batch_manager.progress_updated.connect(progress_dialog.update_progress)
        self.batch_processor.batch_manager.operation_completed.connect(progress_dialog.on_operation_completed)
        self.batch_processor.batch_manager.operation_completed.connect(self.on_test_completed)
        
        # 连接取消信号
        progress_dialog.cancel_requested.connect(self.batch_processor.cancel_batch_operation)
        
        # 启动批量操作
        success, message = self.batch_processor.batch_update_status(violations, 'ignored')
        
        if success:
            progress_dialog.exec_()
        else:
            print(f"无法启动批量状态更新操作：{message}")
    
    def test_export(self):
        """测试导出"""
        violations = self.test_violations  # 全部1000个违例
        
        print(f"开始测试导出：{len(violations)} 个违例")
        
        # 创建进度对话框
        progress_dialog = BatchProgressDialog(self, "测试批量导出")
        
        # 连接信号
        self.batch_processor.batch_manager.progress_updated.connect(progress_dialog.update_progress)
        self.batch_processor.batch_manager.operation_completed.connect(progress_dialog.on_operation_completed)
        self.batch_processor.batch_manager.operation_completed.connect(self.on_test_completed)
        
        # 连接取消信号
        progress_dialog.cancel_requested.connect(self.batch_processor.cancel_batch_operation)
        
        # 启动批量操作
        success, message = self.batch_processor.batch_export_violations(
            violations, 'csv', 'test_export.csv'
        )
        
        if success:
            progress_dialog.exec_()
        else:
            print(f"无法启动批量导出操作：{message}")
    
    def on_test_completed(self, success: bool, message: str):
        """测试完成回调"""
        print(f"测试完成：{'成功' if success else '失败'} - {message}")
        print(f"确认记录数：{len(self.data_model.confirmations)}")
        print(f"模式记录数：{len(self.data_model.patterns)}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()