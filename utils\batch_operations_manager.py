"""
批量操作管理器
用于处理大规模违例数据的批量操作，包括分块处理、进度跟踪和取消功能
"""
import time
import threading
from typing import List, Dict, Callable, Optional, Any, Tuple
from dataclasses import dataclass
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread
from PyQt5.QtWidgets import QApplication


@dataclass
class BatchOperationConfig:
    """批量操作配置"""
    chunk_size: int = 100  # 每个分块的大小
    max_concurrent_chunks: int = 1  # 最大并发分块数
    progress_update_interval: int = 50  # 进度更新间隔（毫秒）
    memory_threshold_mb: float = 500.0  # 内存阈值（MB）
    enable_cancellation: bool = True  # 是否启用取消功能
    enable_recovery: bool = True  # 是否启用恢复功能
    cancellation_granularity: str = "chunk"  # 取消粒度 ("item", "chunk", "operation")
    recovery_checkpoint_interval: int = 10  # 恢复检查点间隔（分块数）


@dataclass
class BatchOperationProgress:
    """批量操作进度信息"""
    total_items: int = 0
    processed_items: int = 0
    current_chunk: int = 0
    total_chunks: int = 0
    success_count: int = 0
    error_count: int = 0
    elapsed_time: float = 0.0
    estimated_remaining_time: float = 0.0
    current_operation: str = ""
    is_cancelling: bool = False
    can_be_cancelled: bool = True
    recovery_available: bool = False
    last_checkpoint: int = 0
    
    @property
    def progress_percentage(self) -> float:
        """计算进度百分比"""
        if self.total_items == 0:
            return 0.0
        return (self.processed_items / self.total_items) * 100.0
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        if self.processed_items == 0:
            return 0.0
        return (self.success_count / self.processed_items) * 100.0
    
    @property
    def items_per_second(self) -> float:
        """计算处理速度（项/秒）"""
        if self.elapsed_time <= 0:
            return 0.0
        return self.processed_items / self.elapsed_time


class BatchOperationWorker(QThread):
    """批量操作工作线程"""
    
    progress_updated = pyqtSignal(BatchOperationProgress)
    operation_completed = pyqtSignal(bool, str)  # success, message
    chunk_completed = pyqtSignal(int, int, int)  # chunk_index, success_count, error_count
    cancellation_confirmed = pyqtSignal()
    recovery_checkpoint_created = pyqtSignal(int, dict)  # checkpoint_id, recovery_data
    
    def __init__(self, items: List[Any], operation_func: Callable, config: BatchOperationConfig):
        super().__init__()
        self.items = items
        self.operation_func = operation_func
        self.config = config
        self.is_cancelled = False
        self.is_cancelling = False
        self.progress = BatchOperationProgress()
        self.start_time = 0.0
        self.recovery_data = {}
        self.processed_items_ids = set()  # 跟踪已处理的项目ID
        
    def run(self):
        """执行批量操作"""
        self.start_time = time.time()
        self.progress.total_items = len(self.items)
        self.progress.total_chunks = (len(self.items) + self.config.chunk_size - 1) // self.config.chunk_size
        self.progress.can_be_cancelled = self.config.enable_cancellation
        self.progress.recovery_available = self.config.enable_recovery
        
        try:
            # 分块处理
            for chunk_index in range(0, len(self.items), self.config.chunk_size):
                # 检查取消状态
                if self.is_cancelled:
                    self._handle_cancellation()
                    return
                
                chunk_end = min(chunk_index + self.config.chunk_size, len(self.items))
                chunk_items = self.items[chunk_index:chunk_end]
                
                self.progress.current_chunk = chunk_index // self.config.chunk_size + 1
                self.progress.current_operation = f"处理第 {self.progress.current_chunk}/{self.progress.total_chunks} 块"
                
                # 处理当前分块
                chunk_success_count, chunk_error_count = self._process_chunk_with_cancellation(chunk_items)
                
                # 更新进度
                self.progress.processed_items = chunk_end
                self.progress.success_count += chunk_success_count
                self.progress.error_count += chunk_error_count
                self.progress.elapsed_time = time.time() - self.start_time
                
                # 估算剩余时间
                if self.progress.processed_items > 0:
                    avg_time_per_item = self.progress.elapsed_time / self.progress.processed_items
                    remaining_items = self.progress.total_items - self.progress.processed_items
                    self.progress.estimated_remaining_time = avg_time_per_item * remaining_items
                
                # 创建恢复检查点
                if (self.config.enable_recovery and 
                    self.progress.current_chunk % self.config.recovery_checkpoint_interval == 0):
                    self._create_recovery_checkpoint()
                
                # 发送进度更新
                self.progress_updated.emit(self.progress)
                self.chunk_completed.emit(chunk_index // self.config.chunk_size, chunk_success_count, chunk_error_count)
                
                # 短暂休眠以避免UI阻塞
                self.msleep(10)
            
            # 操作完成
            success_message = f"批量操作完成：成功 {self.progress.success_count} 项，失败 {self.progress.error_count} 项"
            self.operation_completed.emit(True, success_message)
            
        except Exception as e:
            error_message = f"批量操作失败：{str(e)}"
            self.operation_completed.emit(False, error_message)
    
    def _process_chunk_with_cancellation(self, chunk_items: List[Any]) -> Tuple[int, int]:
        """处理单个分块（支持取消）"""
        success_count = 0
        error_count = 0
        
        for i, item in enumerate(chunk_items):
            # 检查取消状态（项目级别的取消）
            if self.config.cancellation_granularity == "item" and self.is_cancelled:
                break
            
            try:
                result = self.operation_func(item)
                if result:
                    success_count += 1
                    # 记录已处理的项目（用于恢复）
                    if hasattr(item, 'get') and item.get('id'):
                        self.processed_items_ids.add(item.get('id'))
                else:
                    error_count += 1
            except Exception:
                error_count += 1
            
            # 检查分块级别的取消
            if self.config.cancellation_granularity == "chunk" and self.is_cancelled:
                break
        
        return success_count, error_count
    
    def _handle_cancellation(self):
        """处理取消操作"""
        self.progress.is_cancelling = True
        self.progress.current_operation = "正在取消操作..."
        self.progress_updated.emit(self.progress)
        
        # 保存当前状态用于可能的恢复
        if self.config.enable_recovery:
            self._create_recovery_checkpoint()
        
        # 确认取消
        self.cancellation_confirmed.emit()
        
        # 发送取消完成信号
        cancel_message = f"操作已取消：已处理 {self.progress.processed_items} 项，成功 {self.progress.success_count} 项"
        self.operation_completed.emit(False, cancel_message)
    
    def _create_recovery_checkpoint(self):
        """创建恢复检查点"""
        checkpoint_data = {
            'processed_items': self.progress.processed_items,
            'success_count': self.progress.success_count,
            'error_count': self.progress.error_count,
            'current_chunk': self.progress.current_chunk,
            'processed_items_ids': list(self.processed_items_ids),
            'timestamp': time.time()
        }
        
        self.recovery_data[self.progress.current_chunk] = checkpoint_data
        self.progress.last_checkpoint = self.progress.current_chunk
        
        # 发送检查点创建信号
        self.recovery_checkpoint_created.emit(self.progress.current_chunk, checkpoint_data)
    
    def cancel(self):
        """取消操作"""
        if not self.config.enable_cancellation:
            return False
        
        self.is_cancelled = True
        self.is_cancelling = True
        return True
    
    def can_cancel(self) -> bool:
        """检查是否可以取消"""
        return self.config.enable_cancellation and not self.is_cancelling
    
    def get_recovery_data(self) -> Dict:
        """获取恢复数据"""
        return self.recovery_data


class BatchOperationsManager(QObject):
    """批量操作管理器"""
    
    progress_updated = pyqtSignal(BatchOperationProgress)
    operation_completed = pyqtSignal(bool, str)
    cancellation_confirmed = pyqtSignal()
    recovery_checkpoint_created = pyqtSignal(int, dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_worker = None
        self.config = BatchOperationConfig()
        self.recovery_data = {}
        self.operation_history = []
        
    def configure(self, config: BatchOperationConfig):
        """配置批量操作参数"""
        self.config = config
    
    def start_batch_operation(self, items: List[Any], operation_func: Callable, 
                            operation_name: str = "批量操作") -> bool:
        """
        启动批量操作
        
        Args:
            items: 要处理的项目列表
            operation_func: 处理单个项目的函数
            operation_name: 操作名称
            
        Returns:
            bool: 是否成功启动
        """
        if self.current_worker and self.current_worker.isRunning():
            return False  # 已有操作在进行中
        
        # 根据项目数量调整配置
        self._adjust_config_for_size(len(items))
        
        # 创建工作线程
        self.current_worker = BatchOperationWorker(items, operation_func, self.config)
        self.current_worker.progress_updated.connect(self.progress_updated.emit)
        self.current_worker.operation_completed.connect(self._on_operation_completed)
        self.current_worker.chunk_completed.connect(self._on_chunk_completed)
        self.current_worker.cancellation_confirmed.connect(self.cancellation_confirmed.emit)
        self.current_worker.recovery_checkpoint_created.connect(self._on_recovery_checkpoint_created)
        
        # 保存恢复数据
        if self.config.enable_recovery:
            self.recovery_data = {
                'operation_name': operation_name,
                'total_items': len(items),
                'start_time': time.time(),
                'items': items,
                'operation_func': operation_func
            }
        
        # 记录操作历史
        self.operation_history.append({
            'name': operation_name,
            'start_time': time.time(),
            'item_count': len(items),
            'status': 'running'
        })
        
        # 启动操作
        self.current_worker.start()
        return True
    
    def cancel_operation(self) -> bool:
        """取消当前操作"""
        if self.current_worker and self.current_worker.isRunning():
            if self.current_worker.can_cancel():
                return self.current_worker.cancel()
        return False
    
    def can_cancel_current_operation(self) -> bool:
        """检查当前操作是否可以取消"""
        if self.current_worker and self.current_worker.isRunning():
            return self.current_worker.can_cancel()
        return False
    
    def is_operation_running(self) -> bool:
        """检查是否有操作正在运行"""
        return self.current_worker and self.current_worker.isRunning()
    
    def has_recovery_data(self) -> bool:
        """检查是否有可恢复的数据"""
        return bool(self.recovery_data) and self.config.enable_recovery
    
    def get_recovery_info(self) -> Dict:
        """获取恢复信息"""
        if not self.has_recovery_data():
            return {}
        
        recovery_info = {
            'operation_name': self.recovery_data.get('operation_name', ''),
            'total_items': self.recovery_data.get('total_items', 0),
            'interrupted_time': self.recovery_data.get('start_time', 0),
            'has_checkpoints': bool(self.current_worker and self.current_worker.get_recovery_data())
        }
        
        if self.current_worker:
            worker_recovery_data = self.current_worker.get_recovery_data()
            if worker_recovery_data:
                latest_checkpoint = max(worker_recovery_data.keys())
                checkpoint_data = worker_recovery_data[latest_checkpoint]
                recovery_info.update({
                    'last_checkpoint': latest_checkpoint,
                    'processed_items': checkpoint_data.get('processed_items', 0),
                    'success_count': checkpoint_data.get('success_count', 0),
                    'error_count': checkpoint_data.get('error_count', 0)
                })
        
        return recovery_info
    
    def resume_operation_from_checkpoint(self, checkpoint_id: int = None) -> bool:
        """从检查点恢复操作"""
        if not self.has_recovery_data():
            return False
        
        if self.current_worker and self.current_worker.isRunning():
            return False  # 已有操作在进行中
        
        # 获取恢复数据
        items = self.recovery_data.get('items', [])
        operation_func = self.recovery_data.get('operation_func')
        operation_name = self.recovery_data.get('operation_name', '恢复的批量操作')
        
        if not items or not operation_func:
            return False
        
        # 如果指定了检查点，从该检查点恢复
        if checkpoint_id is not None and self.current_worker:
            worker_recovery_data = self.current_worker.get_recovery_data()
            if checkpoint_id in worker_recovery_data:
                checkpoint_data = worker_recovery_data[checkpoint_id]
                processed_ids = set(checkpoint_data.get('processed_items_ids', []))
                # 过滤掉已处理的项目
                items = [item for item in items 
                        if not (hasattr(item, 'get') and item.get('id') in processed_ids)]
        
        # 重新启动操作
        return self.start_batch_operation(items, operation_func, f"{operation_name} (恢复)")
    
    def clear_recovery_data(self):
        """清除恢复数据"""
        self.recovery_data.clear()
    
    def get_operation_history(self) -> List[Dict]:
        """获取操作历史"""
        return self.operation_history.copy()
    
    def _adjust_config_for_size(self, item_count: int):
        """根据项目数量调整配置"""
        if item_count < 1000:
            # 小数据集：较小的分块，快速处理
            self.config.chunk_size = min(50, item_count)
            self.config.cancellation_granularity = "item"
        elif item_count < 10000:
            # 中等数据集：平衡的分块大小
            self.config.chunk_size = 100
            self.config.cancellation_granularity = "chunk"
        else:
            # 大数据集：较大的分块，减少开销
            self.config.chunk_size = min(500, item_count // 20)
            self.config.cancellation_granularity = "chunk"
            self.config.recovery_checkpoint_interval = 5  # 更频繁的检查点
    
    def _on_operation_completed(self, success: bool, message: str):
        """操作完成处理"""
        # 更新操作历史
        if self.operation_history:
            self.operation_history[-1]['status'] = 'completed' if success else 'failed'
            self.operation_history[-1]['end_time'] = time.time()
            self.operation_history[-1]['message'] = message
        
        if self.config.enable_recovery and success:
            # 清除恢复数据
            self.recovery_data.clear()
        
        self.operation_completed.emit(success, message)
    
    def _on_chunk_completed(self, chunk_index: int, success_count: int, error_count: int):
        """分块完成处理"""
        # 可以在这里添加分块级别的处理逻辑
        pass
    
    def _on_recovery_checkpoint_created(self, checkpoint_id: int, checkpoint_data: Dict):
        """恢复检查点创建处理"""
        self.recovery_checkpoint_created.emit(checkpoint_id, checkpoint_data)


class ViolationBatchProcessor:
    """违例批量处理器 - 支持多种批量操作"""
    
    def __init__(self, data_model, parent_window=None):
        self.data_model = data_model
        self.parent_window = parent_window
        self.batch_manager = BatchOperationsManager()
        
    def batch_confirm_violations(self, violations: List[Dict], confirmation_data: Dict,
                               progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """
        批量确认违例
        
        Args:
            violations: 违例列表
            confirmation_data: 确认数据 (confirmer, result, reason)
            progress_callback: 进度回调函数
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        if not violations:
            return False, "没有要确认的违例"
        
        # 配置批量操作
        config = BatchOperationConfig()
        config.chunk_size = self._calculate_optimal_chunk_size(len(violations))
        config.enable_cancellation = True
        config.enable_recovery = True
        
        self.batch_manager.configure(config)
        
        # 连接进度回调
        if progress_callback:
            self.batch_manager.progress_updated.connect(progress_callback)
        
        # 定义确认操作函数
        def confirm_single_violation(violation: Dict) -> bool:
            """确认单个违例"""
            try:
                if violation.get('status') != 'pending':
                    return False  # 跳过非待确认状态的违例
                
                violation_id = violation.get('id')
                if not violation_id:
                    return False
                
                # 更新确认状态
                update_result = self.data_model.update_confirmation(
                    violation_id,
                    status='confirmed',
                    confirmer=confirmation_data['confirmer'],
                    result=confirmation_data['result'],
                    reason=confirmation_data['reason'],
                    is_auto=False
                )
                
                # 处理返回结果
                if isinstance(update_result, tuple):
                    success, _ = update_result
                else:
                    success = update_result
                
                if success:
                    # 保存到历史模式
                    self.data_model.save_pattern(
                        violation.get('hier', ''),
                        violation.get('check_info', ''),
                        confirmation_data['confirmer'],
                        confirmation_data['result'],
                        confirmation_data['reason']
                    )
                    return True
                
                return False
                
            except Exception:
                return False
        
        # 启动批量操作
        return self.batch_manager.start_batch_operation(
            violations, 
            confirm_single_violation,
            "批量确认违例"
        ), "批量确认操作已启动"
    
    def batch_update_status(self, violations: List[Dict], new_status: str,
                          progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """
        批量更新违例状态
        
        Args:
            violations: 违例列表
            new_status: 新状态 ('pending', 'confirmed', 'ignored')
            progress_callback: 进度回调函数
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        if not violations:
            return False, "没有要更新的违例"
        
        # 配置批量操作
        config = BatchOperationConfig()
        config.chunk_size = self._calculate_optimal_chunk_size(len(violations))
        config.enable_cancellation = True
        config.enable_recovery = True
        
        self.batch_manager.configure(config)
        
        # 连接进度回调
        if progress_callback:
            self.batch_manager.progress_updated.connect(progress_callback)
        
        # 定义状态更新操作函数
        def update_single_violation_status(violation: Dict) -> bool:
            """更新单个违例状态"""
            try:
                violation_id = violation.get('id')
                if not violation_id:
                    return False
                
                # 更新状态
                if hasattr(self.data_model, 'update_violation_status'):
                    success = self.data_model.update_violation_status(violation_id, new_status)
                else:
                    # 使用通用的更新方法
                    update_result = self.data_model.update_confirmation(
                        violation_id,
                        status=new_status,
                        confirmer='',
                        result='',
                        reason='批量状态更新',
                        is_auto=True
                    )
                    
                    if isinstance(update_result, tuple):
                        success, _ = update_result
                    else:
                        success = update_result
                
                return success
                
            except Exception:
                return False
        
        # 启动批量操作
        return self.batch_manager.start_batch_operation(
            violations, 
            update_single_violation_status,
            f"批量更新状态为{new_status}"
        ), "批量状态更新操作已启动"
    
    def batch_export_violations(self, violations: List[Dict], export_format: str = 'csv',
                              output_file: str = None, progress_callback: Optional[Callable] = None) -> Tuple[bool, str]:
        """
        批量导出违例数据
        
        Args:
            violations: 违例列表
            export_format: 导出格式 ('csv', 'excel', 'json')
            output_file: 输出文件路径
            progress_callback: 进度回调函数
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        if not violations:
            return False, "没有要导出的违例"
        
        if not output_file:
            return False, "未指定输出文件"
        
        # 配置批量操作
        config = BatchOperationConfig()
        config.chunk_size = self._calculate_export_chunk_size(len(violations), export_format)
        config.enable_cancellation = True
        config.enable_recovery = True
        
        self.batch_manager.configure(config)
        
        # 连接进度回调
        if progress_callback:
            self.batch_manager.progress_updated.connect(progress_callback)
        
        # 准备导出数据结构
        export_data = []
        
        # 定义导出操作函数
        def process_violation_for_export(violation: Dict) -> bool:
            """处理单个违例用于导出"""
            try:
                # 提取需要导出的字段
                export_item = {
                    'ID': violation.get('id', ''),
                    'Hierarchy': violation.get('hier', ''),
                    'Check': violation.get('check_info', ''),
                    'Status': violation.get('status', ''),
                    'Slack': violation.get('slack', ''),
                    'Required': violation.get('required', ''),
                    'Actual': violation.get('actual', ''),
                    'Confirmer': violation.get('confirmer', ''),
                    'Result': violation.get('result', ''),
                    'Reason': violation.get('reason', ''),
                    'Timestamp': violation.get('timestamp', '')
                }
                
                export_data.append(export_item)
                return True
                
            except Exception:
                return False
        
        # 启动批量处理
        success, message = self.batch_manager.start_batch_operation(
            violations, 
            process_violation_for_export,
            f"批量导出违例数据({export_format})"
        ), "批量导出操作已启动"
        
        if success:
            # 在操作完成后执行实际的文件写入
            self.batch_manager.operation_completed.connect(
                lambda success, msg: self._write_export_file(export_data, output_file, export_format) if success else None
            )
        
        return success, message
    
    def _write_export_file(self, data: List[Dict], output_file: str, export_format: str):
        """写入导出文件"""
        try:
            if export_format.lower() == 'csv':
                import csv
                with open(output_file, 'w', newline='', encoding='utf-8') as f:
                    if data:
                        writer = csv.DictWriter(f, fieldnames=data[0].keys())
                        writer.writeheader()
                        writer.writerows(data)
            
            elif export_format.lower() == 'json':
                import json
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            
            elif export_format.lower() == 'excel':
                try:
                    import pandas as pd
                    df = pd.DataFrame(data)
                    df.to_excel(output_file, index=False)
                except ImportError:
                    # 如果没有pandas，回退到CSV
                    csv_file = output_file.replace('.xlsx', '.csv').replace('.xls', '.csv')
                    self._write_export_file(data, csv_file, 'csv')
            
        except Exception as e:
            print(f"导出文件写入失败: {e}")
    
    def _calculate_optimal_chunk_size(self, violation_count: int) -> int:
        """计算最优分块大小"""
        if violation_count < 500:
            return 25
        elif violation_count < 2000:
            return 50
        elif violation_count < 10000:
            return 100
        else:
            return 200
    
    def _calculate_export_chunk_size(self, violation_count: int, export_format: str) -> int:
        """计算导出操作的最优分块大小"""
        base_size = self._calculate_optimal_chunk_size(violation_count)
        
        # 根据导出格式调整分块大小
        if export_format.lower() == 'excel':
            return min(base_size, 1000)  # Excel处理较慢，使用较小分块
        elif export_format.lower() == 'json':
            return base_size * 2  # JSON处理较快，可以使用较大分块
        else:  # CSV
            return base_size
    
    def cancel_batch_operation(self) -> bool:
        """取消批量操作"""
        return self.batch_manager.cancel_operation()
    
    def is_batch_running(self) -> bool:
        """检查批量操作是否正在运行"""
        return self.batch_manager.is_operation_running()