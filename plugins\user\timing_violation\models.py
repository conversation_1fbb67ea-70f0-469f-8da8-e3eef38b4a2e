"""
时序违例确认插件数据模型

包含数据库操作、数据结构定义等功能。
"""

import os
import shutil
import sqlite3
import threading
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any
from PyQt5.QtCore import QObject, pyqtSignal


class ViolationDataModel(QObject):
    """时序违例数据模型"""
    
    # 信号定义
    violation_added = pyqtSignal(dict)      # 违例添加信号
    violation_updated = pyqtSignal(dict)    # 违例更新信号
    confirmation_updated = pyqtSignal(dict) # 确认更新信号
    data_loaded = pyqtSignal(list)          # 数据加载信号
    
    def __init__(self):
        super().__init__()
        self.db_path = self._get_database_path()
        self._lock = threading.Lock()
        self.init_database()
    
    def _get_database_path(self) -> str:
        """获取数据库文件路径"""
        # 数据库位于VIOLATION_CHECK目录下
        base_dir = os.path.join(os.getcwd(), "VIOLATION_CHECK")
        os.makedirs(base_dir, exist_ok=True)
        return os.path.join(base_dir, "timing_violations.db")
    
    def init_database(self):
        """初始化数据库表结构"""
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 获取SQLite版本信息
                cursor.execute("SELECT sqlite_version()")
                sqlite_version = cursor.fetchone()[0]
                print(f"时序违例插件 - SQLite版本: {sqlite_version}")
                
                # 创建时序违例记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS timing_violations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        case_name TEXT NOT NULL,
                        corner TEXT,
                        num INTEGER NOT NULL,
                        hier TEXT NOT NULL,
                        time_fs INTEGER NOT NULL,
                        time_display TEXT NOT NULL,
                        check_info TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(case_name, corner, num, hier, check_info)
                    )
                ''')
                
                # 创建确认记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS confirmation_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        violation_id INTEGER NOT NULL,
                        status TEXT NOT NULL DEFAULT 'pending',
                        confirmer TEXT,
                        result TEXT,
                        reason TEXT,
                        is_auto_confirmed BOOLEAN DEFAULT 0,
                        confirmed_at TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (violation_id) REFERENCES timing_violations(id)
                    )
                ''')
                
                # 创建历史匹配表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS violation_patterns (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        hier_pattern TEXT NOT NULL,
                        check_pattern TEXT NOT NULL,
                        default_confirmer TEXT,
                        default_result TEXT,
                        default_reason TEXT,
                        match_count INTEGER DEFAULT 1,
                        last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(hier_pattern, check_pattern)
                    )
                ''')
                
                # 创建索引
                index_statements = [
                    'CREATE INDEX IF NOT EXISTS idx_violations_case_corner ON timing_violations(case_name, corner)',
                    'CREATE INDEX IF NOT EXISTS idx_violations_hier_check ON timing_violations(hier, check_info)',
                    'CREATE INDEX IF NOT EXISTS idx_confirmations_violation ON confirmation_records(violation_id)',
                    'CREATE INDEX IF NOT EXISTS idx_patterns_hier_check ON violation_patterns(hier_pattern, check_pattern)'
                ]
                
                for sql in index_statements:
                    try:
                        cursor.execute(sql)
                    except Exception as e:
                        print(f"创建索引失败: {e}")
                        # 索引创建失败不影响整体功能
                
                conn.commit()
                print("时序违例插件数据库初始化成功")
                
            except Exception as e:
                print(f"时序违例插件数据库初始化失败: {str(e)}")
                conn.rollback()
                raise
            finally:
                conn.close()
    
    def add_violations(self, violations: List[Dict], case_name: str, corner: str, file_path: str) -> int:
        """批量添加时序违例记录
        
        Args:
            violations: 违例列表
            case_name: 用例名称
            corner: Corner信息
            file_path: 日志文件路径
            
        Returns:
            int: 成功添加的记录数
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                success_count = 0
                
                for violation in violations:
                    try:
                        # 检查记录是否已存在
                        cursor.execute('''
                            SELECT id FROM timing_violations 
                            WHERE case_name = ? AND corner = ? AND num = ? AND hier = ? AND check_info = ?
                        ''', (case_name, corner, violation['NUM'], violation['Hier'], violation['Check']))
                        
                        if cursor.fetchone():
                            continue  # 记录已存在，跳过
                        
                        # 插入新记录
                        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        cursor.execute('''
                            INSERT INTO timing_violations
                            (case_name, corner, num, hier, time_fs, time_display, check_info, file_path, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            case_name, corner, violation['NUM'], violation['Hier'],
                            violation['time_fs'], violation['Time'], violation['Check'],
                            file_path, now
                        ))
                        
                        violation_id = cursor.lastrowid
                        
                        # 创建对应的确认记录
                        cursor.execute('''
                            INSERT INTO confirmation_records
                            (violation_id, status, created_at, updated_at)
                            VALUES (?, 'pending', ?, ?)
                        ''', (violation_id, now, now))
                        
                        success_count += 1
                        
                        # 发送信号
                        violation_data = {
                            'id': violation_id,
                            'case_name': case_name,
                            'corner': corner,
                            'num': violation['NUM'],
                            'hier': violation['Hier'],
                            'time_fs': violation['time_fs'],
                            'time_display': violation['Time'],
                            'check_info': violation['Check'],
                            'status': 'pending'
                        }
                        self.violation_added.emit(violation_data)
                        
                    except Exception as e:
                        print(f"添加违例记录失败: {str(e)}")
                        continue
                
                conn.commit()
                return success_count
                
            except Exception as e:
                conn.rollback()
                print(f"批量添加违例记录失败: {str(e)}")
                return 0
            finally:
                conn.close()
    
    def get_violations_by_case(self, case_name: str, corner: str = None) -> List[Dict]:
        """获取指定用例的违例记录
        
        Args:
            case_name: 用例名称
            corner: Corner信息（可选）
            
        Returns:
            List[Dict]: 违例记录列表
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                if corner:
                    sql = '''
                        SELECT v.*, c.status, c.confirmer, c.result, c.reason, c.is_auto_confirmed, c.confirmed_at
                        FROM timing_violations v
                        LEFT JOIN confirmation_records c ON v.id = c.violation_id
                        WHERE v.case_name = ? AND v.corner = ?
                        ORDER BY v.num
                    '''
                    cursor.execute(sql, (case_name, corner))
                else:
                    sql = '''
                        SELECT v.*, c.status, c.confirmer, c.result, c.reason, c.is_auto_confirmed, c.confirmed_at
                        FROM timing_violations v
                        LEFT JOIN confirmation_records c ON v.id = c.violation_id
                        WHERE v.case_name = ?
                        ORDER BY v.num
                    '''
                    cursor.execute(sql, (case_name,))
                
                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                
                violations = []
                for row in rows:
                    violation = dict(zip(columns, row))
                    violations.append(violation)
                
                return violations
                
            except Exception as e:
                print(f"获取违例记录失败: {str(e)}")
                return []
            finally:
                conn.close()

    def update_confirmation(self, violation_id: int, status: str, confirmer: str = None,
                          result: str = None, reason: str = None, is_auto: bool = False) -> bool:
        """更新确认记录

        Args:
            violation_id: 违例记录ID
            status: 确认状态 (pending/confirmed/ignored)
            confirmer: 确认人
            result: 确认结果 (pass/issue)
            reason: 确认理由
            is_auto: 是否自动确认

        Returns:
            bool: 是否成功更新
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 更新确认记录
                cursor.execute('''
                    UPDATE confirmation_records
                    SET status = ?, confirmer = ?, result = ?, reason = ?,
                        is_auto_confirmed = ?, confirmed_at = ?, updated_at = ?
                    WHERE violation_id = ?
                ''', (status, confirmer, result, reason, is_auto, now, now, violation_id))

                conn.commit()

                # 准备信号数据，但在锁外发送
                confirmation_data = {
                    'violation_id': violation_id,
                    'status': status,
                    'confirmer': confirmer,
                    'result': result,
                    'reason': reason,
                    'is_auto_confirmed': is_auto,
                    'confirmed_at': now
                }

                return True, confirmation_data

            except Exception as e:
                conn.rollback()
                print(f"更新确认记录失败: {str(e)}")
                return False, None
            finally:
                conn.close()

        # 在锁外发送信号，避免死锁
        if 'confirmation_data' in locals():
            try:
                self.confirmation_updated.emit(confirmation_data)
            except Exception as e:
                print(f"发送确认更新信号失败: {str(e)}")

    def auto_confirm_by_reset_time(self, case_name: str, corner: str, reset_time_ns: float) -> int:
        """根据复位时间自动确认违例

        Args:
            case_name: 用例名称
            corner: Corner信息
            reset_time_ns: 复位时间（纳秒）

        Returns:
            int: 自动确认的记录数
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 转换时间单位：纳秒 -> 飞秒
                reset_time_fs = int(reset_time_ns * 1000000)
                print(f"自动确认参数 - 用例: {case_name}, corner: {corner}, 复位时间: {reset_time_ns}ns ({reset_time_fs}fs)")

                # 智能corner匹配：首先尝试指定corner，如果没找到则尝试default corner
                violations_to_confirm = []
                corners_to_try = [corner] if corner != "default" else ["default"]

                # 如果指定的corner不是default，则在找不到记录时回退到default
                if corner != "default":
                    corners_to_try.append("default")

                for try_corner in corners_to_try:
                    # 查找需要自动确认的违例（时间小于等于复位时间且状态为pending）
                    cursor.execute('''
                        SELECT v.id, v.num, v.hier, v.time_fs, v.time_display, v.corner
                        FROM timing_violations v
                        LEFT JOIN confirmation_records c ON v.id = c.violation_id
                        WHERE v.case_name = ? AND v.corner = ? AND v.time_fs <= ?
                        AND (c.status = 'pending' OR c.status IS NULL)
                    ''', (case_name, try_corner, reset_time_fs))

                    current_violations = cursor.fetchall()

                    if current_violations:
                        violations_to_confirm = current_violations
                        if try_corner != corner:
                            print(f"在corner '{corner}' 中未找到记录，回退到corner '{try_corner}' 找到 {len(violations_to_confirm)} 条违例")
                        else:
                            print(f"在corner '{try_corner}' 中找到 {len(violations_to_confirm)} 条需要自动确认的违例")
                        break
                    else:
                        print(f"在corner '{try_corner}' 中未找到符合条件的违例记录")

                if not violations_to_confirm:
                    print("在所有可能的corner中都没有找到符合条件的违例记录")
                    return 0

                # 批量更新确认记录
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                reason = f"复位期间时序违例（<= {reset_time_ns}ns），可以忽略"
                confirmed_count = 0

                for violation_row in violations_to_confirm:
                    violation_id, num, hier, time_fs, time_display, actual_corner = violation_row
                    time_ns = time_fs / 1000000  # 转换为纳秒显示

                    print(f"自动确认违例 - NUM: {num}, Hier: {hier}, Time: {time_display} ({time_ns:.3f}ns), Corner: {actual_corner}")

                    # 检查是否已有确认记录
                    cursor.execute('SELECT id FROM confirmation_records WHERE violation_id = ?', (violation_id,))
                    existing_record = cursor.fetchone()

                    if existing_record:
                        # 更新现有确认记录
                        cursor.execute('''
                            UPDATE confirmation_records
                            SET status = 'confirmed', result = 'pass', reason = ?,
                                confirmer = '系统自动', is_auto_confirmed = 1,
                                confirmed_at = ?, updated_at = ?
                            WHERE violation_id = ?
                        ''', (reason, now, now, violation_id))
                    else:
                        # 创建新的确认记录
                        cursor.execute('''
                            INSERT INTO confirmation_records
                            (violation_id, status, result, reason, confirmer, is_auto_confirmed,
                             confirmed_at, created_at, updated_at)
                            VALUES (?, 'confirmed', 'pass', ?, '系统自动', 1, ?, ?, ?)
                        ''', (violation_id, reason, now, now, now))

                    confirmed_count += 1

                conn.commit()
                print(f"成功自动确认 {confirmed_count} 条违例记录")
                return confirmed_count

            except Exception as e:
                conn.rollback()
                print(f"自动确认失败: {str(e)}")
                import traceback
                traceback.print_exc()
                return 0
            finally:
                conn.close()

    def auto_confirm_by_reset_time_and_interval(self, case_name: str, corner: str,
                                               reset_time_ns: float = None,
                                               interval_start_ns: float = None,
                                               interval_end_ns: float = None) -> int:
        """根据复位时间和复位区间自动确认违例

        Args:
            case_name: 用例名称
            corner: Corner信息
            reset_time_ns: 复位时间（纳秒），可选
            interval_start_ns: 复位区间开始时间（纳秒），可选
            interval_end_ns: 复位区间结束时间（纳秒），可选

        Returns:
            int: 自动确认的记录数
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 构建查询条件
                conditions = []
                base_params = [case_name]

                # 添加复位时间条件（时间小于等于复位时间）
                if reset_time_ns is not None:
                    reset_time_fs = int(reset_time_ns * 1000000)
                    conditions.append("v.time_fs <= ?")
                    base_params.append(reset_time_fs)
                    print(f"添加复位时间条件: <= {reset_time_ns}ns ({reset_time_fs}fs)")

                # 添加复位区间条件（时间在区间内）
                if interval_start_ns is not None and interval_end_ns is not None:
                    interval_start_fs = int(interval_start_ns * 1000000)
                    interval_end_fs = int(interval_end_ns * 1000000)
                    conditions.append("(v.time_fs >= ? AND v.time_fs <= ?)")
                    base_params.extend([interval_start_fs, interval_end_fs])
                    print(f"添加复位区间条件: {interval_start_ns}ns~{interval_end_ns}ns ({interval_start_fs}fs~{interval_end_fs}fs)")

                if not conditions:
                    print("没有提供任何确认条件")
                    return 0

                # 使用OR连接条件，满足任一条件即可确认
                time_condition = " OR ".join(f"({cond})" for cond in conditions)

                print(f"自动确认参数 - 用例: {case_name}, corner: {corner}")
                print(f"时间条件: {time_condition}")

                # 智能corner匹配：首先尝试指定corner，如果没找到则尝试default corner
                violations_to_confirm = []
                corners_to_try = [corner] if corner != "default" else ["default"]

                # 如果指定的corner不是default，则在找不到记录时回退到default
                if corner != "default":
                    corners_to_try.append("default")

                for try_corner in corners_to_try:
                    params = base_params.copy()
                    params.insert(1, try_corner)  # 在case_name后插入corner参数

                    # 查找需要自动确认的违例
                    query = f'''
                        SELECT v.id, v.num, v.hier, v.time_fs, v.time_display, v.corner
                        FROM timing_violations v
                        LEFT JOIN confirmation_records c ON v.id = c.violation_id
                        WHERE v.case_name = ? AND v.corner = ? AND ({time_condition})
                        AND (c.status = 'pending' OR c.status IS NULL)
                    '''

                    cursor.execute(query, params)
                    current_violations = cursor.fetchall()

                    if current_violations:
                        violations_to_confirm = current_violations
                        if try_corner != corner:
                            print(f"在corner '{corner}' 中未找到记录，回退到corner '{try_corner}' 找到 {len(violations_to_confirm)} 条违例")
                        else:
                            print(f"在corner '{try_corner}' 中找到 {len(violations_to_confirm)} 条需要自动确认的违例")
                        break
                    else:
                        print(f"在corner '{try_corner}' 中未找到符合条件的违例记录")

                if not violations_to_confirm:
                    print("在所有可能的corner中都没有找到符合条件的违例记录")
                    return 0

                # 批量更新确认记录
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 构建确认理由
                reason_parts = []
                if reset_time_ns is not None:
                    reason_parts.append(f"复位期间时序违例（<= {reset_time_ns}ns）")
                if interval_start_ns is not None and interval_end_ns is not None:
                    reason_parts.append(f"复位区间内时序违例（{interval_start_ns}ns~{interval_end_ns}ns）")

                reason = "，".join(reason_parts) + "，可以忽略"
                confirmed_count = 0

                for violation_row in violations_to_confirm:
                    violation_id, num, hier, time_fs, time_display, actual_corner = violation_row
                    time_ns = time_fs / 1000000  # 转换为纳秒显示

                    print(f"自动确认违例 - NUM: {num}, Hier: {hier}, Time: {time_display} ({time_ns:.3f}ns), Corner: {actual_corner}")

                    # 检查是否已有确认记录
                    cursor.execute('SELECT id FROM confirmation_records WHERE violation_id = ?', (violation_id,))
                    existing_record = cursor.fetchone()

                    if existing_record:
                        # 更新现有确认记录
                        cursor.execute('''
                            UPDATE confirmation_records
                            SET status = 'confirmed', result = 'pass', reason = ?,
                                confirmer = '系统自动', is_auto_confirmed = 1,
                                confirmed_at = ?, updated_at = ?
                            WHERE violation_id = ?
                        ''', (reason, now, now, violation_id))
                    else:
                        # 创建新的确认记录
                        cursor.execute('''
                            INSERT INTO confirmation_records
                            (violation_id, status, result, reason, confirmer, is_auto_confirmed,
                             confirmed_at, created_at, updated_at)
                            VALUES (?, 'confirmed', 'pass', ?, '系统自动', 1, ?, ?, ?)
                        ''', (violation_id, reason, now, now, now))

                    confirmed_count += 1

                conn.commit()
                print(f"成功自动确认 {confirmed_count} 条违例记录")
                return confirmed_count

            except Exception as e:
                conn.rollback()
                print(f"自动确认失败: {str(e)}")
                import traceback
                traceback.print_exc()
                return 0
            finally:
                conn.close()

    def save_pattern(self, hier: str, check: str, confirmer: str, result: str, reason: str):
        """保存确认模式到历史记录

        Args:
            hier: 层级路径
            check: 检查信息
            confirmer: 确认人
            result: 确认结果
            reason: 确认理由
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                # 检查模式是否已存在
                cursor.execute('''
                    SELECT id, match_count FROM violation_patterns
                    WHERE hier_pattern = ? AND check_pattern = ?
                ''', (hier, check))

                existing = cursor.fetchone()

                if existing:
                    # 更新现有模式
                    pattern_id, match_count = existing
                    cursor.execute('''
                        UPDATE violation_patterns
                        SET default_confirmer = ?, default_result = ?, default_reason = ?,
                            match_count = ?, last_used = ?
                        WHERE id = ?
                    ''', (confirmer, result, reason, match_count + 1, now, pattern_id))
                else:
                    # 插入新模式
                    cursor.execute('''
                        INSERT INTO violation_patterns
                        (hier_pattern, check_pattern, default_confirmer, default_result,
                         default_reason, match_count, last_used)
                        VALUES (?, ?, ?, ?, ?, 1, ?)
                    ''', (hier, check, confirmer, result, reason, now))

                conn.commit()

            except Exception as e:
                conn.rollback()
                print(f"保存确认模式失败: {str(e)}")
            finally:
                conn.close()

    def _normalize_check_info(self, check_info: str) -> str:
        """标准化检查信息，用于模糊匹配

        规则：
        1. 层级路径必须完全匹配
        2. 括号前的内容必须完全匹配（检查类型必须相同）
        3. 检查信息括号内容按逗号分割为三部分：
           - 第一部分：冒号后面的时间信息忽略，只匹配冒号前面的内容
           - 第二部分：冒号后面的时间信息忽略，只匹配冒号前面的内容
           - 第三部分：整个部分都忽略（不参与匹配）

        Args:
            check_info: 原始检查信息

        Returns:
            str: 标准化后的检查信息
        """
        try:
            # 查找括号内容
            start_idx = check_info.find('(')
            end_idx = check_info.rfind(')')

            if start_idx == -1 or end_idx == -1 or start_idx >= end_idx:
                # 如果没有找到括号，返回原始信息
                return check_info

            # 提取括号前的部分和括号内的部分
            prefix = check_info[:start_idx + 1]  # 包含开括号，这部分必须完全匹配
            bracket_content = check_info[start_idx + 1:end_idx]

            # 按逗号分割括号内容
            parts = bracket_content.split(',')

            if len(parts) < 3:
                # 如果分割后少于3部分，返回原始信息
                return check_info

            normalized_parts = []

            # 处理第一部分：移除冒号后的时间信息
            part1 = parts[0].strip()
            colon_idx = part1.find(':')
            if colon_idx != -1:
                part1 = part1[:colon_idx].strip()
            normalized_parts.append(part1)

            # 处理第二部分：移除冒号后的时间信息
            part2 = parts[1].strip()
            colon_idx = part2.find(':')
            if colon_idx != -1:
                part2 = part2[:colon_idx].strip()
            normalized_parts.append(part2)

            # 第三部分忽略，不添加到标准化结果中

            # 重新组装标准化的检查信息
            # 注意：括号前的内容(prefix)保持不变，必须完全匹配
            normalized_bracket_content = ', '.join(normalized_parts)
            normalized_check_info = prefix + normalized_bracket_content + ')'

            return normalized_check_info

        except Exception as e:
            print(f"标准化检查信息失败: {str(e)}, 原始信息: {check_info}")
            return check_info

    def get_pattern_suggestions(self, hier: str, check: str) -> Optional[Dict]:
        """获取历史模式建议

        Args:
            hier: 层级路径
            check: 检查信息

        Returns:
            Optional[Dict]: 建议信息，如果没有匹配返回None
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 首先尝试精确匹配
                cursor.execute('''
                    SELECT default_confirmer, default_result, default_reason, match_count
                    FROM violation_patterns
                    WHERE hier_pattern = ? AND check_pattern = ?
                    ORDER BY last_used DESC
                    LIMIT 1
                ''', (hier, check))

                result = cursor.fetchone()
                if result:
                    return {
                        'confirmer': result[0],
                        'result': result[1],
                        'reason': result[2],
                        'match_count': result[3]
                    }

                # 如果精确匹配失败，尝试模糊匹配
                normalized_check = self._normalize_check_info(check)

                # 获取所有相同层级的历史模式
                cursor.execute('''
                    SELECT check_pattern, default_confirmer, default_result, default_reason, match_count
                    FROM violation_patterns
                    WHERE hier_pattern = ?
                    ORDER BY last_used DESC
                ''', (hier,))

                patterns = cursor.fetchall()

                # 对每个历史模式进行模糊匹配
                for pattern_check, confirmer, result, reason, match_count in patterns:
                    normalized_pattern = self._normalize_check_info(pattern_check)

                    if normalized_pattern == normalized_check:
                        print(f"模糊匹配成功: {hier}")
                        print(f"  当前检查信息: {check}")
                        print(f"  历史模式: {pattern_check}")
                        print(f"  标准化匹配: {normalized_check}")

                        return {
                            'confirmer': confirmer,
                            'result': result,
                            'reason': reason,
                            'match_count': match_count
                        }

                return None

            except Exception as e:
                print(f"获取模式建议失败: {str(e)}")
                return None
            finally:
                conn.close()

    def apply_historical_confirmations(self, case_name: str, corner: str = None) -> int:
        """自动应用历史确认记录（corner无关版本）

        Args:
            case_name: 用例名称
            corner: Corner信息（可选，为了兼容性保留，但不再用于过滤）

        Returns:
            int: 自动应用的确认记录数
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 获取当前用例的待确认违例（移除corner过滤，实现corner无关）
                if corner:
                    # 如果指定了corner，仍然按corner过滤（向后兼容）
                    cursor.execute('''
                        SELECT v.id, v.hier, v.check_info, v.corner
                        FROM timing_violations v
                        JOIN confirmation_records c ON v.id = c.violation_id
                        WHERE v.case_name = ? AND v.corner = ? AND c.status = 'pending'
                    ''', (case_name, corner))
                else:
                    # 如果没有指定corner，获取所有corner的待确认违例
                    cursor.execute('''
                        SELECT v.id, v.hier, v.check_info, v.corner
                        FROM timing_violations v
                        JOIN confirmation_records c ON v.id = c.violation_id
                        WHERE v.case_name = ? AND c.status = 'pending'
                    ''', (case_name,))

                pending_violations = cursor.fetchall()

                if not pending_violations:
                    return 0

                applied_count = 0
                now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                for violation_id, hier, check_info, violation_corner in pending_violations:
                    # 查找匹配的历史模式（不再依赖corner）
                    # 首先尝试精确匹配
                    cursor.execute('''
                        SELECT default_confirmer, default_result, default_reason, match_count, check_pattern
                        FROM violation_patterns
                        WHERE hier_pattern = ? AND check_pattern = ?
                        ORDER BY last_used DESC
                        LIMIT 1
                    ''', (hier, check_info))

                    pattern = cursor.fetchone()
                    matched_pattern_check = None

                    if pattern:
                        confirmer, result, reason, match_count, matched_pattern_check = pattern
                    else:
                        # 如果精确匹配失败，尝试模糊匹配
                        normalized_check = self._normalize_check_info(check_info)

                        # 获取所有相同层级的历史模式
                        cursor.execute('''
                            SELECT check_pattern, default_confirmer, default_result, default_reason, match_count
                            FROM violation_patterns
                            WHERE hier_pattern = ?
                            ORDER BY last_used DESC
                        ''', (hier,))

                        patterns = cursor.fetchall()

                        # 对每个历史模式进行模糊匹配
                        for pattern_check, p_confirmer, p_result, p_reason, p_match_count in patterns:
                            normalized_pattern = self._normalize_check_info(pattern_check)

                            if normalized_pattern == normalized_check:
                                confirmer, result, reason, match_count = p_confirmer, p_result, p_reason, p_match_count
                                matched_pattern_check = pattern_check
                                pattern = (confirmer, result, reason, match_count, matched_pattern_check)
                                print(f"模糊匹配应用历史确认 (corner: {violation_corner}): {hier}")
                                print(f"  当前检查信息: {check_info}")
                                print(f"  匹配的历史模式: {pattern_check}")
                                break

                    if pattern:
                        # 应用历史确认信息
                        cursor.execute('''
                            UPDATE confirmation_records
                            SET status = 'confirmed', confirmer = ?, result = ?, reason = ?,
                                is_auto_confirmed = 0, confirmed_at = ?, updated_at = ?
                            WHERE violation_id = ?
                        ''', (confirmer, result, reason, now, now, violation_id))

                        # 更新模式使用次数（使用匹配到的历史模式的check_pattern）
                        cursor.execute('''
                            UPDATE violation_patterns
                            SET match_count = ?, last_used = ?
                            WHERE hier_pattern = ? AND check_pattern = ?
                        ''', (match_count + 1, now, hier, matched_pattern_check))

                        applied_count += 1
                        if matched_pattern_check == check_info:
                            print(f"精确匹配应用历史确认 (corner: {violation_corner}): {hier} - {check_info}")
                        else:
                            print(f"模糊匹配应用历史确认 (corner: {violation_corner}): {hier} - {check_info}")

                conn.commit()
                print(f"历史确认应用完成，共应用 {applied_count} 条记录（corner无关模式）")
                return applied_count

            except Exception as e:
                conn.rollback()
                print(f"应用历史确认失败: {str(e)}")
                return 0
            finally:
                conn.close()

    def get_all_patterns(self) -> List[Dict]:
        """获取所有历史确认模式

        Returns:
            List[Dict]: 历史模式列表
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT hier_pattern, check_pattern, default_confirmer,
                           default_result, default_reason, match_count, last_used
                    FROM violation_patterns
                    ORDER BY last_used DESC
                ''')

                rows = cursor.fetchall()
                patterns = []

                for row in rows:
                    pattern = {
                        'hier_pattern': row[0],
                        'check_pattern': row[1],
                        'default_confirmer': row[2],
                        'default_result': row[3],
                        'default_reason': row[4],
                        'match_count': row[5],
                        'last_used': row[6]
                    }
                    patterns.append(pattern)

                return patterns

            except Exception as e:
                print(f"获取历史模式失败: {str(e)}")
                return []
            finally:
                conn.close()

    def clear_all_patterns(self) -> bool:
        """清除所有历史确认模式

        Returns:
            bool: 是否成功清除
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM violation_patterns')
                conn.commit()
                print("已清除所有历史确认模式")
                return True

            except Exception as e:
                conn.rollback()
                print(f"清除历史模式失败: {str(e)}")
                return False
            finally:
                conn.close()

    def export_patterns_to_excel(self, file_path: str) -> bool:
        """导出历史确认模式到Excel文件

        Args:
            file_path: 导出文件路径

        Returns:
            bool: 是否成功导出
        """
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill
        except ImportError:
            raise ImportError("需要安装openpyxl库: pip install openpyxl")

        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 获取所有历史模式
                cursor.execute('''
                    SELECT hier_pattern, check_pattern, default_confirmer,
                           default_result, default_reason, match_count, last_used
                    FROM violation_patterns
                    ORDER BY last_used DESC
                ''')

                patterns = cursor.fetchall()

                if not patterns:
                    print("没有历史确认模式可导出")
                    return False

                # 创建工作簿和工作表
                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = "历史确认模式"

                # 设置表头
                headers = ["层级路径", "检查信息", "确认人", "确认结果", "确认理由", "使用次数", "最后使用时间"]
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, column=col, value=header)
                    cell.font = Font(bold=True)
                    cell.alignment = Alignment(horizontal='center')
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

                # 填充数据
                for row, pattern in enumerate(patterns, 2):
                    hier_pattern, check_pattern, confirmer, result, reason, match_count, last_used = pattern

                    # 转换确认结果显示
                    result_display = "通过" if result == "pass" else "有问题" if result == "issue" else result

                    ws.cell(row=row, column=1, value=hier_pattern)
                    ws.cell(row=row, column=2, value=check_pattern)
                    ws.cell(row=row, column=3, value=confirmer)
                    ws.cell(row=row, column=4, value=result_display)
                    ws.cell(row=row, column=5, value=reason)
                    ws.cell(row=row, column=6, value=match_count)
                    ws.cell(row=row, column=7, value=last_used)

                # 自动调整列宽
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                    ws.column_dimensions[column_letter].width = adjusted_width

                # 保存文件
                wb.save(file_path)
                print(f"历史确认模式已导出到: {file_path}")
                return True

            except Exception as e:
                print(f"导出Excel失败: {str(e)}")
                return False
            finally:
                conn.close()

    def export_patterns_to_csv(self, file_path: str) -> bool:
        """导出历史确认模式到CSV文件

        Args:
            file_path: 导出文件路径

        Returns:
            bool: 是否成功导出
        """
        import csv

        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 获取所有历史模式
                cursor.execute('''
                    SELECT hier_pattern, check_pattern, default_confirmer,
                           default_result, default_reason, match_count, last_used
                    FROM violation_patterns
                    ORDER BY last_used DESC
                ''')

                patterns = cursor.fetchall()

                if not patterns:
                    print("没有历史确认模式可导出")
                    return False

                # 写入CSV文件
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入表头
                    headers = ["层级路径", "检查信息", "确认人", "确认结果", "确认理由", "使用次数", "最后使用时间"]
                    writer.writerow(headers)

                    # 写入数据
                    for pattern in patterns:
                        hier_pattern, check_pattern, confirmer, result, reason, match_count, last_used = pattern

                        # 转换确认结果显示
                        result_display = "通过" if result == "pass" else "有问题" if result == "issue" else result

                        writer.writerow([
                            hier_pattern, check_pattern, confirmer, result_display,
                            reason, match_count, last_used
                        ])

                print(f"历史确认模式已导出到: {file_path}")
                return True

            except Exception as e:
                print(f"导出CSV失败: {str(e)}")
                return False
            finally:
                conn.close()

    def update_case_corner(self, case_name: str, old_corner: str, new_corner: str) -> bool:
        """更新用例的corner信息

        Args:
            case_name: 用例名称
            old_corner: 原corner
            new_corner: 新corner

        Returns:
            bool: 是否成功更新
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                # 更新违例记录的corner
                cursor.execute('''
                    UPDATE timing_violations
                    SET corner = ?
                    WHERE case_name = ? AND corner = ?
                ''', (new_corner, case_name, old_corner))

                updated_count = cursor.rowcount
                conn.commit()

                print(f"更新corner: {case_name} 从 '{old_corner}' 到 '{new_corner}', 影响 {updated_count} 条记录")
                return updated_count > 0

            except Exception as e:
                conn.rollback()
                print(f"更新corner失败: {str(e)}")
                return False
            finally:
                conn.close()

    def clear_case_data(self, case_name: str, corner: str = None):
        """清除指定用例的数据

        Args:
            case_name: 用例名称
            corner: Corner信息（可选）
        """
        with self._lock:
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()

                if corner:
                    # 删除确认记录
                    cursor.execute('''
                        DELETE FROM confirmation_records
                        WHERE violation_id IN (
                            SELECT id FROM timing_violations
                            WHERE case_name = ? AND corner = ?
                        )
                    ''', (case_name, corner))

                    # 删除违例记录
                    cursor.execute('''
                        DELETE FROM timing_violations
                        WHERE case_name = ? AND corner = ?
                    ''', (case_name, corner))
                else:
                    # 删除确认记录
                    cursor.execute('''
                        DELETE FROM confirmation_records
                        WHERE violation_id IN (
                            SELECT id FROM timing_violations
                            WHERE case_name = ?
                        )
                    ''', (case_name,))

                    # 删除违例记录
                    cursor.execute('''
                        DELETE FROM timing_violations
                        WHERE case_name = ?
                    ''', (case_name,))

                conn.commit()
                print(f"已清除用例 {case_name} 的数据")

            except Exception as e:
                conn.rollback()
                print(f"清除用例数据失败: {str(e)}")
            finally:
                conn.close()

    # ==================== 数据库合并功能 ====================

    def backup_database(self) -> str:
        """备份当前数据库

        Returns:
            str: 备份文件路径，失败时返回空字符串
        """
        try:
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = os.path.dirname(self.db_path)
            backup_filename = f"timing_violations_backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_filename)

            # 复制数据库文件
            shutil.copy2(self.db_path, backup_path)
            print(f"数据库备份成功: {backup_path}")
            return backup_path

        except Exception as e:
            print(f"数据库备份失败: {str(e)}")
            return ""

    def validate_database_schema(self, db_path: str) -> bool:
        """验证数据库schema兼容性

        Args:
            db_path: 数据库文件路径

        Returns:
            bool: 是否兼容
        """
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 检查必要的表是否存在
            required_tables = ['timing_violations', 'confirmation_records', 'violation_patterns']
            for table in required_tables:
                cursor.execute("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name=?
                """, (table,))
                if not cursor.fetchone():
                    print(f"数据库缺少必要的表: {table}")
                    return False

            # 检查timing_violations表的关键字段
            cursor.execute("PRAGMA table_info(timing_violations)")
            columns = [row[1] for row in cursor.fetchall()]
            required_columns = ['id', 'case_name', 'corner', 'num', 'hier', 'time_fs', 'check_info']
            for col in required_columns:
                if col not in columns:
                    print(f"timing_violations表缺少必要字段: {col}")
                    return False

            conn.close()
            return True

        except Exception as e:
            print(f"验证数据库schema失败: {str(e)}")
            return False

    def merge_databases(self, source_db_paths: List[str], progress_callback=None) -> Dict[str, Any]:
        """合并多个数据库到当前数据库

        Args:
            source_db_paths: 源数据库文件路径列表
            progress_callback: 进度回调函数，接收(current, total, message)参数

        Returns:
            Dict: 合并结果统计信息
        """
        merge_stats = {
            'success': False,
            'backup_path': '',
            'total_violations_added': 0,
            'total_confirmations_updated': 0,
            'total_patterns_merged': 0,
            'errors': [],
            'processed_databases': 0
        }

        try:
            # 1. 备份当前数据库
            if progress_callback:
                progress_callback(0, len(source_db_paths) + 1, "正在备份当前数据库...")

            backup_path = self.backup_database()
            if not backup_path:
                merge_stats['errors'].append("数据库备份失败")
                return merge_stats
            merge_stats['backup_path'] = backup_path

            # 2. 验证所有源数据库
            valid_sources = []
            for i, db_path in enumerate(source_db_paths):
                if progress_callback:
                    progress_callback(i + 1, len(source_db_paths) + 1, f"验证数据库: {os.path.basename(db_path)}")

                if not os.path.exists(db_path):
                    merge_stats['errors'].append(f"数据库文件不存在: {db_path}")
                    continue

                if not self.validate_database_schema(db_path):
                    merge_stats['errors'].append(f"数据库schema不兼容: {db_path}")
                    continue

                valid_sources.append(db_path)

            if not valid_sources:
                merge_stats['errors'].append("没有有效的源数据库")
                return merge_stats

            # 3. 执行合并
            for i, source_db in enumerate(valid_sources):
                if progress_callback:
                    progress_callback(i + 1, len(valid_sources), f"合并数据库: {os.path.basename(source_db)}")

                result = self._merge_single_database(source_db)
                merge_stats['total_violations_added'] += result['violations_added']
                merge_stats['total_confirmations_updated'] += result['confirmations_updated']
                merge_stats['total_patterns_merged'] += result['patterns_merged']
                merge_stats['processed_databases'] += 1

                if result['errors']:
                    merge_stats['errors'].extend(result['errors'])

            merge_stats['success'] = True
            if progress_callback:
                progress_callback(len(valid_sources), len(valid_sources), "合并完成")

        except Exception as e:
            error_msg = f"数据库合并过程中发生错误: {str(e)}"
            print(error_msg)
            merge_stats['errors'].append(error_msg)

        return merge_stats

    def _merge_single_database(self, source_db_path: str) -> Dict[str, Any]:
        """合并单个数据库

        Args:
            source_db_path: 源数据库路径

        Returns:
            Dict: 合并结果
        """
        result = {
            'violations_added': 0,
            'confirmations_updated': 0,
            'patterns_merged': 0,
            'errors': []
        }

        with self._lock:
            target_conn = sqlite3.connect(self.db_path)
            source_conn = sqlite3.connect(source_db_path)

            try:
                target_cursor = target_conn.cursor()
                source_cursor = source_conn.cursor()

                # 1. 合并timing_violations表
                source_cursor.execute("""
                    SELECT case_name, corner, num, hier, time_fs, time_display,
                           check_info, file_path, created_at
                    FROM timing_violations
                """)

                violations_data = source_cursor.fetchall()
                for violation in violations_data:
                    try:
                        # 检查是否已存在相同的违例记录
                        target_cursor.execute("""
                            SELECT id FROM timing_violations
                            WHERE case_name=? AND corner=? AND num=? AND hier=? AND check_info=?
                        """, (violation[0], violation[1], violation[2], violation[3], violation[6]))

                        existing = target_cursor.fetchone()
                        if not existing:
                            # 插入新的违例记录
                            target_cursor.execute("""
                                INSERT INTO timing_violations
                                (case_name, corner, num, hier, time_fs, time_display,
                                 check_info, file_path, created_at)
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, violation)

                            new_violation_id = target_cursor.lastrowid
                            result['violations_added'] += 1

                            # 为新违例创建确认记录
                            now = datetime.now().isoformat()
                            target_cursor.execute("""
                                INSERT INTO confirmation_records
                                (violation_id, status, created_at, updated_at)
                                VALUES (?, 'pending', ?, ?)
                            """, (new_violation_id, now, now))

                    except Exception as e:
                        result['errors'].append(f"合并违例记录失败: {str(e)}")
                        continue

                # 2. 合并确认记录（更新已存在违例的确认信息）
                result['confirmations_updated'] += self._merge_confirmation_records(
                    source_cursor, target_cursor
                )

                # 3. 合并历史模式
                result['patterns_merged'] += self._merge_violation_patterns(
                    source_cursor, target_cursor
                )

                target_conn.commit()

            except Exception as e:
                target_conn.rollback()
                error_msg = f"合并数据库 {source_db_path} 失败: {str(e)}"
                result['errors'].append(error_msg)
                print(error_msg)
            finally:
                target_conn.close()
                source_conn.close()

        return result

    def _merge_confirmation_records(self, source_cursor, target_cursor) -> int:
        """合并确认记录

        Args:
            source_cursor: 源数据库游标
            target_cursor: 目标数据库游标

        Returns:
            int: 更新的确认记录数量
        """
        updated_count = 0

        try:
            # 获取源数据库中已确认的记录
            source_cursor.execute("""
                SELECT v.case_name, v.corner, v.num, v.hier, v.check_info,
                       c.status, c.confirmer, c.result, c.reason,
                       c.is_auto_confirmed, c.confirmed_at
                FROM timing_violations v
                JOIN confirmation_records c ON v.id = c.violation_id
                WHERE c.status != 'pending'
            """)

            confirmed_records = source_cursor.fetchall()

            for record in confirmed_records:
                # 查找目标数据库中对应的违例记录
                target_cursor.execute("""
                    SELECT v.id, c.status FROM timing_violations v
                    JOIN confirmation_records c ON v.id = c.violation_id
                    WHERE v.case_name=? AND v.corner=? AND v.num=?
                          AND v.hier=? AND v.check_info=?
                """, (record[0], record[1], record[2], record[3], record[4]))

                target_record = target_cursor.fetchone()
                if target_record:
                    violation_id, current_status = target_record

                    # 如果目标记录未确认，或源记录更完整，则更新
                    if (current_status == 'pending' or
                        (record[5] == 'confirmed' and record[6] and record[7])):

                        target_cursor.execute("""
                            UPDATE confirmation_records
                            SET status=?, confirmer=?, result=?, reason=?,
                                is_auto_confirmed=?, confirmed_at=?,
                                updated_at=?
                            WHERE violation_id=?
                        """, (record[5], record[6], record[7], record[8],
                              record[9], record[10], datetime.now().isoformat(),
                              violation_id))

                        updated_count += 1

        except Exception as e:
            print(f"合并确认记录失败: {str(e)}")

        return updated_count

    def _merge_violation_patterns(self, source_cursor, target_cursor) -> int:
        """合并违例模式

        Args:
            source_cursor: 源数据库游标
            target_cursor: 目标数据库游标

        Returns:
            int: 合并的模式数量
        """
        merged_count = 0

        try:
            # 获取源数据库中的所有模式
            source_cursor.execute("""
                SELECT hier_pattern, check_pattern, default_confirmer,
                       default_result, default_reason, match_count, last_used
                FROM violation_patterns
            """)

            patterns = source_cursor.fetchall()

            for pattern in patterns:
                # 检查目标数据库中是否已存在相同模式
                target_cursor.execute("""
                    SELECT id, match_count, last_used FROM violation_patterns
                    WHERE hier_pattern=? AND check_pattern=?
                """, (pattern[0], pattern[1]))

                existing = target_cursor.fetchone()

                if existing:
                    # 更新现有模式：合并使用次数，更新最后使用时间
                    pattern_id, current_count, current_last_used = existing
                    new_count = current_count + pattern[5]

                    # 选择更新的最后使用时间
                    source_last_used = pattern[6]
                    if source_last_used and (not current_last_used or source_last_used > current_last_used):
                        new_last_used = source_last_used
                    else:
                        new_last_used = current_last_used

                    target_cursor.execute("""
                        UPDATE violation_patterns
                        SET match_count=?, last_used=?
                        WHERE id=?
                    """, (new_count, new_last_used, pattern_id))

                else:
                    # 插入新模式
                    target_cursor.execute("""
                        INSERT INTO violation_patterns
                        (hier_pattern, check_pattern, default_confirmer,
                         default_result, default_reason, match_count, last_used)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, pattern)

                merged_count += 1

        except Exception as e:
            print(f"合并违例模式失败: {str(e)}")

        return merged_count

    def get_database_statistics(self, db_path: str = None) -> Dict[str, int]:
        """获取数据库统计信息

        Args:
            db_path: 数据库路径，为None时使用当前数据库

        Returns:
            Dict: 统计信息
        """
        stats = {
            'total_violations': 0,
            'confirmed_violations': 0,
            'pending_violations': 0,
            'total_patterns': 0,
            'unique_cases': 0
        }

        target_db = db_path if db_path else self.db_path

        try:
            conn = sqlite3.connect(target_db)
            cursor = conn.cursor()

            # 总违例数
            cursor.execute("SELECT COUNT(*) FROM timing_violations")
            stats['total_violations'] = cursor.fetchone()[0]

            # 已确认违例数
            cursor.execute("""
                SELECT COUNT(*) FROM confirmation_records
                WHERE status = 'confirmed'
            """)
            stats['confirmed_violations'] = cursor.fetchone()[0]

            # 待确认违例数
            cursor.execute("""
                SELECT COUNT(*) FROM confirmation_records
                WHERE status = 'pending'
            """)
            stats['pending_violations'] = cursor.fetchone()[0]

            # 总模式数
            cursor.execute("SELECT COUNT(*) FROM violation_patterns")
            stats['total_patterns'] = cursor.fetchone()[0]

            # 唯一用例数
            cursor.execute("SELECT COUNT(DISTINCT case_name) FROM timing_violations")
            stats['unique_cases'] = cursor.fetchone()[0]

            conn.close()

        except Exception as e:
            print(f"获取数据库统计信息失败: {str(e)}")

        return stats
