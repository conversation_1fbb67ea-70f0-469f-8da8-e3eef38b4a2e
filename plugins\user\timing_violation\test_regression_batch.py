#!/usr/bin/env python3
"""
回归批量功能测试脚本

测试回归目录扫描和批量处理功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from regression_scanner import RegressionDirectoryScanner, RegressionFileInfo
from regression_batch_manager import RegressionBatchManager


def create_test_regression_structure():
    """创建测试用的回归目录结构"""
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="test_regression_")
    print(f"创建测试目录: {temp_dir}")
    
    # 创建回归目录结构
    test_structure = {
        "cpu_sys": {
            "cpu_test_ss": {
                "cpu_test_seed1": "log/vio_summary.log",
                "cpu_test_seed2": "log/vio_summary.log"
            },
            "cpu_test_ff": {
                "cpu_test_seed1": "log/vio_summary.log",
                "cpu_test_seed3": "log/vio_summary.log"
            }
        },
        "memory_sys": {
            "mem_test_tt": {
                "mem_test_seed1": "log/vio_summary.log",
                "mem_test_seed2": "log/vio_summary.log"
            }
        },
        "top": {
            "integration_test_ss": {
                "integration_test_seed1": "log/vio_summary.log"
            }
        }
    }
    
    # 创建目录和文件
    for subsys, corners in test_structure.items():
        for corner, seeds in corners.items():
            for seed, log_path in seeds.items():
                full_path = os.path.join(temp_dir, subsys, corner, seed, log_path)
                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                
                # 创建示例vio_summary.log文件
                with open(full_path, 'w') as f:
                    f.write(create_sample_vio_log(subsys, corner.split('_')[-1], seed.split('_')[-1]))
    
    return temp_dir


def create_sample_vio_log(subsys, corner, seed):
    """创建示例违例日志内容"""
    return f"""# Timing Violation Summary
# Subsystem: {subsys}
# Corner: {corner}
# Seed: {seed}

NUM  Hier                                    Time        Check
1    {subsys}.cpu.clk_domain                100.5 NS    setup
2    {subsys}.cpu.data_path                 150.2 NS    hold
3    {subsys}.memory.ctrl                   200.8 NS    setup
4    {subsys}.interface.bus                 75.3 NS     hold
5    {subsys}.debug.trace                   300.1 NS    setup
"""


def test_regression_scanner():
    """测试回归扫描器"""
    print("\n=== 测试回归扫描器 ===")
    
    # 创建测试目录
    test_dir = create_test_regression_structure()
    
    try:
        # 创建扫描器
        scanner = RegressionDirectoryScanner()
        
        # 扫描目录
        print(f"扫描目录: {test_dir}")
        result = scanner.scan_regression_directory(test_dir)
        
        # 输出结果
        print(f"扫描完成，耗时: {result.scan_time:.2f} 秒")
        print(f"发现文件数: {len(result.valid_files)}")
        print(f"无效路径数: {len(result.invalid_paths)}")
        
        print("\n子系统分组:")
        for subsys, files in result.subsys_groups.items():
            print(f"  {subsys}: {len(files)} 个文件")
        
        print("\n工艺角分组:")
        for corner, files in result.corner_groups.items():
            print(f"  {corner}: {len(files)} 个文件")
        
        print("\n用例分组:")
        for case, files in result.case_groups.items():
            print(f"  {case}: {len(files)} 个文件")
        
        print("\n详细文件信息:")
        for file_info in result.valid_files:
            print(f"  {file_info.subsys}/{file_info.corner_name}/{file_info.case_name}_{file_info.seed}")
        
        return result
        
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)
        print(f"清理测试目录: {test_dir}")


def test_batch_manager(scan_result):
    """测试批量管理器"""
    print("\n=== 测试批量管理器 ===")
    
    # 创建批量管理器
    manager = RegressionBatchManager()
    manager.set_scan_result(scan_result)
    
    # 测试过滤器
    print("\n可用子系统:", manager.get_subsys_list())
    print("可用工艺角:", manager.get_corner_list())
    print("可用用例:", manager.get_case_list())
    
    # 测试选择
    print("\n测试文件选择:")
    all_files = manager.get_files_by_filters()
    print(f"过滤后文件数: {len(all_files)}")
    
    # 选择前两个文件
    if len(all_files) >= 2:
        selected_paths = [f.file_path for f in all_files[:2]]
        manager.select_files(selected_paths)
        
        selected_files = manager.get_selected_files()
        print(f"已选择文件数: {len(selected_files)}")
        
        stats = manager.get_statistics()
        print("统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
    
    # 测试过滤器
    print("\n测试子系统过滤:")
    manager.set_subsys_filter(["cpu_sys"])
    filtered_files = manager.get_files_by_filters()
    print(f"cpu_sys 过滤后文件数: {len(filtered_files)}")
    
    print("\n测试工艺角过滤:")
    manager.set_corner_filter(["ss"])
    filtered_files = manager.get_files_by_filters()
    print(f"ss 工艺角过滤后文件数: {len(filtered_files)}")


def main():
    """主测试函数"""
    print("开始回归批量功能测试")
    
    try:
        # 测试扫描器
        scan_result = test_regression_scanner()
        
        # 测试批量管理器
        test_batch_manager(scan_result)
        
        print("\n=== 测试完成 ===")
        print("所有测试通过！")
        
    except Exception as e:
        print(f"\n测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
